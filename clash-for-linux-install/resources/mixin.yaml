# Web 控制台配置
external-controller: "0.0.0.0:9090"
external-ui: public
secret:

# 代理服务器配置
allow-lan: false # 默认关闭，若开启务必设置用户验证以防暴露公网后被滥用
authentication: 
  # - "username:password" # 用户验证（clashon默认填充验证信息）

# tun 配置
tun:
  enable: false
  stack: system
  auto-route: true
  auto-redir: true # clash
  auto-redirect: true # mihomo
  auto-detect-interface: true
  dns-hijack:
    - any:53
    - tcp://any:53
  strict-route: true
  exclude-interface:
    # - docker0
    # - podman0

# DNS 配置
dns:
  enable: true
  listen: 0.0.0.0:1053
  enhanced-mode: fake-ip
  nameserver:
    - ***************
    - *******
